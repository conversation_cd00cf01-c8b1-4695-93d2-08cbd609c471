// WrsDepartmentReport domain entity for wrs module
// 根據 03_domain_model.md 與 domain.schema.ts props 實作
import {  DepartmentReportStatus, WrsDepartmentReportProps } from './domain.schema'
import { BusinessRuleViolationError } from '@/errors/domain.error'

/**
 * WrsDepartmentReport
 * 部門彙整週報領域實體，管理部門摘要、重點、計劃與審閱。
 * - 提交後不可修改，主管可標記審閱。
 * - 彙整時可修改組員 work_item，需記錄 history_ref_id。
 */
export class WrsDepartmentReport {
    /**
     * 建構子
     * @param props 部門週報屬性
     */
    constructor(
        private readonly props: WrsDepartmentReportProps
    ) {}

    /**
     * 取得所有屬性
     */
    getProps(): WrsDepartmentReportProps {
        return this.props;
    }

    /**
     * 提交部門週報
     * @param submittedAt 提交時間（ISO 8601）
     * @throws BusinessRuleViolationError 已提交時不可重複提交
     */
    submit(submittedAt: string): WrsDepartmentReport {
        if (this.props.status === 'submitted') {
            throw new BusinessRuleViolationError('ALREADY_SUBMITTED', 'Department report already submitted');
        }
        return new WrsDepartmentReport({
            ...this.props,
            status: 'submitted',
            submittedAt,
        });
    }

    /**
     * 儲存草稿
     * @throws BusinessRuleViolationError 已提交後不可再儲存草稿
     */
    saveDraft(): WrsDepartmentReport {
        if (this.props.status === 'submitted') {
            throw new BusinessRuleViolationError('CANNOT_MODIFY_SUBMITTED', 'Cannot save draft after submission');
        }
        return new WrsDepartmentReport({
            ...this.props,
            status: 'draft',
        });
    }

    /**
     * 處主管標記已審閱
     * @throws BusinessRuleViolationError 僅能在 submitted 狀態下標記
     */
    markReviewed(): WrsDepartmentReport {
        if (this.props.status !== 'submitted') {
            throw new BusinessRuleViolationError('INVALID_STATUS_FOR_REVIEW', 'Can only review after submission');
        }
        return new WrsDepartmentReport({
            ...this.props,
            reviewedBySupervisor: true,
        });
    }
}
