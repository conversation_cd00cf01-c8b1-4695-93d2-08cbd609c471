// WrsEmployeeReport domain entity for wrs module
// 根據 03_domain_model.md 與 domain.schema.ts props 實作
import { WrsEmployeeReportProps, EmployeeReportStatus } from './domain.schema'
import { BusinessRuleViolationError } from '@/errors/domain.error'

/**
 * WrsEmployeeReport
 * 員工個人週報領域實體，管理週報屬性與狀態轉換。
 * - 提交後不可修改，草稿可多次儲存。
 * - 可複製上次週報內容作為草稿（尚未實作）。
 */
export class WrsEmployeeReport {
    /**
     * 建構子
     * @param props 員工週報屬性
     */
    constructor(
        private readonly props: WrsEmployeeReportProps
    ) {}

    /**
     * 取得所有屬性
     */
    getProps(): WrsEmployeeReportProps {
        return this.props;
    }

    /**
     * 提交週報，狀態轉為 submitted
     * @param submittedAt 提交時間（ISO 8601）
     * @throws BusinessRuleViolationError 已提交時不可重複提交
     */
    submit(submittedAt: string): WrsEmployeeReport {
        if (this.props.status === 'submitted') {
            throw new BusinessRuleViolationError('ALREADY_SUBMITTED', 'Report already submitted');
        }
        return new WrsEmployeeReport({
            ...this.props,
            status: 'submitted',
            submittedAt,
        });
    }

    /**
     * 儲存草稿
     * @throws BusinessRuleViolationError 已提交後不可再儲存草稿
     */
    saveDraft(): WrsEmployeeReport {
        if (this.props.status === 'submitted') {
            throw new BusinessRuleViolationError('CANNOT_MODIFY_SUBMITTED', 'Cannot save draft after submission');
        }
        return new WrsEmployeeReport({
            ...this.props,
            status: 'draft',
        });
    }

    // TODO: 複製上次週報內容作為草稿，需有跨週查詢支援，暫不實作
}
